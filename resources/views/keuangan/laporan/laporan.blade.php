@extends('layouts.contentNavbarLayout')

@section('title', 'Laporan')

@section('content')
<div class="row">
    <div class="col-xxl-8 mb-6 order-0">
        <div class="card">
            <div class="d-flex align-items-start row">
                <div class="col-sm-7">
                    <div class="card-body">
                        <h5 class="card-title text-primary mb-3">Congratulations John! 🎉</h5>
                        <p class="mb-6">You have done 72% more sales today.<br>Check your new badge in your profile.
                        </p>

                        <a href="javascript:;" class="btn btn-sm btn-outline-primary">View Badges</a>
                    </div>
                </div>
                <div class="col-sm-5 text-center text-sm-left">
                    <div class="card-body pb-0 px-0 px-md-6">
                        <img src="http://127.0.0.1:8000/assets/img/illustrations/man-with-laptop.png" height="175" class="scaleX-n1-rtl" alt="View Badge User">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-4 order-1">
        <div class="row">
            <div class="col-lg-6 col-md-12 col-6 mb-6">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="card-title d-flex align-items-start justify-content-between mb-4">
                            <div class="avatar flex-shrink-0">
                                <img src="http://127.0.0.1:8000/assets/img/icons/unicons/chart-success.png" alt="chart success" class="rounded">
                            </div>
                            <div class="dropdown">
                                <button class="btn p-0" type="button" id="cardOpt3" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="bx bx-dots-vertical-rounded text-muted"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="cardOpt3">
                                    <a class="dropdown-item" href="javascript:void(0);">View More</a>
                                    <a class="dropdown-item" href="javascript:void(0);">Delete</a>
                                </div>
                            </div>
                        </div>
                        <p class="mb-1">Profit</p>
                        <h4 class="card-title mb-3">$12,628</h4>
                        <small class="text-success fw-medium"><i class="bx bx-up-arrow-alt"></i> +72.80%</small>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-12 col-6 mb-6">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="card-title d-flex align-items-start justify-content-between mb-4">
                            <div class="avatar flex-shrink-0">
                                <img src="http://127.0.0.1:8000/assets/img/icons/unicons/wallet-info.png" alt="wallet info" class="rounded">
                            </div>
                            <div class="dropdown">
                                <button class="btn p-0" type="button" id="cardOpt6" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="bx bx-dots-vertical-rounded text-muted"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="cardOpt6">
                                    <a class="dropdown-item" href="javascript:void(0);">View More</a>
                                    <a class="dropdown-item" href="javascript:void(0);">Delete</a>
                                </div>
                            </div>
                        </div>
                        <p class="mb-1">Sales</p>
                        <h4 class="card-title mb-3">$4,679</h4>
                        <small class="text-success fw-medium"><i class="bx bx-up-arrow-alt"></i> +28.42%</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Total Revenue -->
    <div class="col-12 col-xxl-8 order-2 order-md-3 order-xxl-2 mb-6">
        <div class="card">
            <div class="row row-bordered g-0">
                <div class="col-lg-8">
                    <div class="card-header d-flex align-items-center justify-content-between">
                        <div class="card-title mb-0">
                            <h5 class="m-0 me-2">Total Revenue</h5>
                        </div>
                        <div class="dropdown">
                            <button class="btn p-0" type="button" id="totalRevenue" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="bx bx-dots-vertical-rounded bx-lg text-muted"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-end" aria-labelledby="totalRevenue">
                                <a class="dropdown-item" href="javascript:void(0);">Select All</a>
                                <a class="dropdown-item" href="javascript:void(0);">Refresh</a>
                                <a class="dropdown-item" href="javascript:void(0);">Share</a>
                            </div>
                        </div>
                    </div>
                    <div id="totalRevenueChart" class="px-3" style="min-height: 332px;"><div id="apexchartsw9gc832wj" class="apexcharts-canvas apexchartsw9gc832wj apexcharts-theme-light" style="width: 895px; height: 317px;"><svg id="SvgjsSvg1629" width="895" height="317" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev" class="apexcharts-svg" xmlns:data="ApexChartsNS" transform="translate(0, 0)" style="background: transparent;"><foreignObject x="0" y="0" width="895" height="317"><div class="apexcharts-legend apexcharts-align-left apx-legend-position-top" xmlns="http://www.w3.org/1999/xhtml" style="right: 0px; position: absolute; left: 0px; top: 4px; max-height: 158.5px;"><div class="apexcharts-legend-series" rel="1" seriesname="2024" data:collapsed="false" style="margin: 2px 10px;"><span class="apexcharts-legend-marker" rel="1" data:collapsed="false" style="background: rgb(105, 108, 255) !important; color: rgb(105, 108, 255); height: 8px; width: 8px; left: -5px; top: 0px; border-width: 0px; border-color: rgb(255, 255, 255); border-radius: 12px;"></span><span class="apexcharts-legend-text" rel="1" i="0" data:default-text="2024" data:collapsed="false" style="color: rgb(100, 110, 120); font-size: 13px; font-weight: 400; font-family: &quot;Public Sans&quot;;">2024</span></div><div class="apexcharts-legend-series" rel="2" seriesname="2023" data:collapsed="false" style="margin: 2px 10px;"><span class="apexcharts-legend-marker" rel="2" data:collapsed="false" style="background: rgb(3, 195, 236) !important; color: rgb(3, 195, 236); height: 8px; width: 8px; left: -5px; top: 0px; border-width: 0px; border-color: rgb(255, 255, 255); border-radius: 12px;"></span><span class="apexcharts-legend-text" rel="2" i="1" data:default-text="2023" data:collapsed="false" style="color: rgb(100, 110, 120); font-size: 13px; font-weight: 400; font-family: &quot;Public Sans&quot;;">2023</span></div></div><style type="text/css">	
    
  .apexcharts-legend {	
    display: flex;	
    overflow: auto;	
    padding: 0 10px;	
  }	
  .apexcharts-legend.apx-legend-position-bottom, .apexcharts-legend.apx-legend-position-top {	
    flex-wrap: wrap	
  }	
  .apexcharts-legend.apx-legend-position-right, .apexcharts-legend.apx-legend-position-left {	
    flex-direction: column;	
    bottom: 0;	
  }	
  .apexcharts-legend.apx-legend-position-bottom.apexcharts-align-left, .apexcharts-legend.apx-legend-position-top.apexcharts-align-left, .apexcharts-legend.apx-legend-position-right, .apexcharts-legend.apx-legend-position-left {	
    justify-content: flex-start;	
  }	
  .apexcharts-legend.apx-legend-position-bottom.apexcharts-align-center, .apexcharts-legend.apx-legend-position-top.apexcharts-align-center {	
    justify-content: center;  	
  }	
  .apexcharts-legend.apx-legend-position-bottom.apexcharts-align-right, .apexcharts-legend.apx-legend-position-top.apexcharts-align-right {	
    justify-content: flex-end;	
  }	
  .apexcharts-legend-series {	
    cursor: pointer;	
    line-height: normal;	
  }	
  .apexcharts-legend.apx-legend-position-bottom .apexcharts-legend-series, .apexcharts-legend.apx-legend-position-top .apexcharts-legend-series{	
    display: flex;	
    align-items: center;	
  }	
  .apexcharts-legend-text {	
    position: relative;	
    font-size: 14px;	
  }	
  .apexcharts-legend-text *, .apexcharts-legend-marker * {	
    pointer-events: none;	
  }	
  .apexcharts-legend-marker {	
    position: relative;	
    display: inline-block;	
    cursor: pointer;	
    margin-right: 3px;	
    border-style: solid;
  }	
      
  .apexcharts-legend.apexcharts-align-right .apexcharts-legend-series, .apexcharts-legend.apexcharts-align-left .apexcharts-legend-series{	
    display: inline-block;	
  }	
  .apexcharts-legend-series.apexcharts-no-click {	
    cursor: auto;	
  }	
  .apexcharts-legend .apexcharts-hidden-zero-series, .apexcharts-legend .apexcharts-hidden-null-series {	
    display: none !important;	
  }	
  .apexcharts-inactive-legend {	
    opacity: 0.45;	
  }</style></foreignObject><g id="SvgjsG1631" class="apexcharts-inner apexcharts-graphical" transform="translate(56.9375, 52)"><defs id="SvgjsDefs1630"><linearGradient id="SvgjsLinearGradient1635" x1="0" y1="0" x2="0" y2="1"><stop id="SvgjsStop1636" stop-opacity="0.4" stop-color="rgba(216,227,240,0.4)" offset="0"></stop><stop id="SvgjsStop1637" stop-opacity="0.5" stop-color="rgba(190,209,230,0.5)" offset="1"></stop><stop id="SvgjsStop1638" stop-opacity="0.5" stop-color="rgba(190,209,230,0.5)" offset="1"></stop></linearGradient><clipPath id="gridRectMaskw9gc832wj"><rect id="SvgjsRect1640" width="828.0625" height="239.73000000000002" x="-5" y="-3" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath><clipPath id="forecastMaskw9gc832wj"></clipPath><clipPath id="nonForecastMaskw9gc832wj"></clipPath><clipPath id="gridRectMarkerMaskw9gc832wj"><rect id="SvgjsRect1641" width="822.0625" height="237.73000000000002" x="-2" y="-2" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath></defs><rect id="SvgjsRect1639" width="38.565803571428575" height="233.73000000000002" x="0" y="0" rx="0" ry="0" opacity="1" stroke-width="0" stroke-dasharray="3" fill="url(#SvgjsLinearGradient1635)" class="apexcharts-xcrosshairs" y2="233.73000000000002" filter="none" fill-opacity="0.9"></rect><g id="SvgjsG1661" class="apexcharts-xaxis" transform="translate(0, 0)"><g id="SvgjsG1662" class="apexcharts-xaxis-texts-g" transform="translate(0, -4)"><text id="SvgjsText1664" font-family="Public Sans" x="58.433035714285715" y="262.73" text-anchor="middle" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-xaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1665">Jan</tspan><title>Jan</title></text><text id="SvgjsText1667" font-family="Public Sans" x="175.29910714285714" y="262.73" text-anchor="middle" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-xaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1668">Feb</tspan><title>Feb</title></text><text id="SvgjsText1670" font-family="Public Sans" x="292.16517857142856" y="262.73" text-anchor="middle" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-xaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1671">Mar</tspan><title>Mar</title></text><text id="SvgjsText1673" font-family="Public Sans" x="409.03125" y="262.73" text-anchor="middle" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-xaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1674">Apr</tspan><title>Apr</title></text><text id="SvgjsText1676" font-family="Public Sans" x="525.8973214285714" y="262.73" text-anchor="middle" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-xaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1677">May</tspan><title>May</title></text><text id="SvgjsText1679" font-family="Public Sans" x="642.7633928571429" y="262.73" text-anchor="middle" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-xaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1680">Jun</tspan><title>Jun</title></text><text id="SvgjsText1682" font-family="Public Sans" x="759.6294642857143" y="262.73" text-anchor="middle" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-xaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1683">Jul</tspan><title>Jul</title></text></g></g><g id="SvgjsG1698" class="apexcharts-grid"><g id="SvgjsG1699" class="apexcharts-gridlines-horizontal"><line id="SvgjsLine1701" x1="0" y1="0" x2="818.0625" y2="0" stroke="#e4e6e8" stroke-dasharray="7" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1702" x1="0" y1="46.746" x2="818.0625" y2="46.746" stroke="#e4e6e8" stroke-dasharray="7" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1703" x1="0" y1="93.492" x2="818.0625" y2="93.492" stroke="#e4e6e8" stroke-dasharray="7" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1704" x1="0" y1="140.238" x2="818.0625" y2="140.238" stroke="#e4e6e8" stroke-dasharray="7" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1705" x1="0" y1="186.984" x2="818.0625" y2="186.984" stroke="#e4e6e8" stroke-dasharray="7" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1706" x1="0" y1="233.73000000000002" x2="818.0625" y2="233.73000000000002" stroke="#e4e6e8" stroke-dasharray="7" stroke-linecap="butt" class="apexcharts-gridline"></line></g><g id="SvgjsG1700" class="apexcharts-gridlines-vertical"></g><line id="SvgjsLine1708" x1="0" y1="233.73000000000002" x2="818.0625" y2="233.73000000000002" stroke="transparent" stroke-dasharray="0" stroke-linecap="butt"></line><line id="SvgjsLine1707" x1="0" y1="1" x2="0" y2="233.73000000000002" stroke="transparent" stroke-dasharray="0" stroke-linecap="butt"></line></g><g id="SvgjsG1642" class="apexcharts-bar-series apexcharts-plot-series"><g id="SvgjsG1643" class="apexcharts-series" seriesName="2024" rel="1" data:realIndex="0"><path id="SvgjsPath1645" d="M 39.15013392857143 128.238L 39.15013392857143 68.09519999999999Q 39.15013392857143 56.09519999999999 51.15013392857143 56.09519999999999L 59.715937499999995 56.09519999999999Q 71.7159375 56.09519999999999 71.7159375 68.09519999999999L 71.7159375 68.09519999999999L 71.7159375 128.238Q 71.7159375 140.238 59.715937499999995 140.238L 51.15013392857143 140.238Q 39.15013392857143 140.238 39.15013392857143 128.238z" fill="rgba(105,108,255,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 39.15013392857143 128.238L 39.15013392857143 68.09519999999999Q 39.15013392857143 56.09519999999999 51.15013392857143 56.09519999999999L 59.715937499999995 56.09519999999999Q 71.7159375 56.09519999999999 71.7159375 68.09519999999999L 71.7159375 68.09519999999999L 71.7159375 128.238Q 71.7159375 140.238 59.715937499999995 140.238L 51.15013392857143 140.238Q 39.15013392857143 140.238 39.15013392857143 128.238z" pathFrom="M 39.15013392857143 128.238L 39.15013392857143 128.238L 71.7159375 128.238L 71.7159375 128.238L 71.7159375 128.238L 71.7159375 128.238L 71.7159375 128.238L 39.15013392857143 128.238" cy="56.09519999999999" cx="153.01620535714287" j="0" val="18" barHeight="84.14280000000001" barWidth="38.565803571428575"></path><path id="SvgjsPath1646" d="M 156.01620535714287 128.238L 156.01620535714287 119.5158Q 156.01620535714287 107.5158 168.01620535714287 107.5158L 176.58200892857144 107.5158Q 188.58200892857144 107.5158 188.58200892857144 119.5158L 188.58200892857144 119.5158L 188.58200892857144 128.238Q 188.58200892857144 140.238 176.58200892857144 140.238L 168.01620535714287 140.238Q 156.01620535714287 140.238 156.01620535714287 128.238z" fill="rgba(105,108,255,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 156.01620535714287 128.238L 156.01620535714287 119.5158Q 156.01620535714287 107.5158 168.01620535714287 107.5158L 176.58200892857144 107.5158Q 188.58200892857144 107.5158 188.58200892857144 119.5158L 188.58200892857144 119.5158L 188.58200892857144 128.238Q 188.58200892857144 140.238 176.58200892857144 140.238L 168.01620535714287 140.238Q 156.01620535714287 140.238 156.01620535714287 128.238z" pathFrom="M 156.01620535714287 128.238L 156.01620535714287 128.238L 188.58200892857144 128.238L 188.58200892857144 128.238L 188.58200892857144 128.238L 188.58200892857144 128.238L 188.58200892857144 128.238L 156.01620535714287 128.238" cy="107.5158" cx="269.8822767857143" j="1" val="7" barHeight="32.7222" barWidth="38.565803571428575"></path><path id="SvgjsPath1647" d="M 272.8822767857143 128.238L 272.8822767857143 82.119Q 272.8822767857143 70.119 284.8822767857143 70.119L 293.4480803571429 70.119Q 305.4480803571429 70.119 305.4480803571429 82.119L 305.4480803571429 82.119L 305.4480803571429 128.238Q 305.4480803571429 140.238 293.4480803571429 140.238L 284.8822767857143 140.238Q 272.8822767857143 140.238 272.8822767857143 128.238z" fill="rgba(105,108,255,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 272.8822767857143 128.238L 272.8822767857143 82.119Q 272.8822767857143 70.119 284.8822767857143 70.119L 293.4480803571429 70.119Q 305.4480803571429 70.119 305.4480803571429 82.119L 305.4480803571429 82.119L 305.4480803571429 128.238Q 305.4480803571429 140.238 293.4480803571429 140.238L 284.8822767857143 140.238Q 272.8822767857143 140.238 272.8822767857143 128.238z" pathFrom="M 272.8822767857143 128.238L 272.8822767857143 128.238L 305.4480803571429 128.238L 305.4480803571429 128.238L 305.4480803571429 128.238L 305.4480803571429 128.238L 305.4480803571429 128.238L 272.8822767857143 128.238" cy="70.119" cx="386.7483482142857" j="2" val="15" barHeight="70.119" barWidth="38.565803571428575"></path><path id="SvgjsPath1648" d="M 389.7483482142857 128.238L 389.7483482142857 16.674599999999998Q 389.7483482142857 4.674599999999998 401.7483482142857 4.674599999999998L 410.31415178571433 4.674599999999998Q 422.31415178571433 4.674599999999998 422.31415178571433 16.674599999999998L 422.31415178571433 16.674599999999998L 422.31415178571433 128.238Q 422.31415178571433 140.238 410.31415178571433 140.238L 401.7483482142857 140.238Q 389.7483482142857 140.238 389.7483482142857 128.238z" fill="rgba(105,108,255,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 389.7483482142857 128.238L 389.7483482142857 16.674599999999998Q 389.7483482142857 4.674599999999998 401.7483482142857 4.674599999999998L 410.31415178571433 4.674599999999998Q 422.31415178571433 4.674599999999998 422.31415178571433 16.674599999999998L 422.31415178571433 16.674599999999998L 422.31415178571433 128.238Q 422.31415178571433 140.238 410.31415178571433 140.238L 401.7483482142857 140.238Q 389.7483482142857 140.238 389.7483482142857 128.238z" pathFrom="M 389.7483482142857 128.238L 389.7483482142857 128.238L 422.31415178571433 128.238L 422.31415178571433 128.238L 422.31415178571433 128.238L 422.31415178571433 128.238L 422.31415178571433 128.238L 389.7483482142857 128.238" cy="4.674599999999998" cx="503.6144196428572" j="3" val="29" barHeight="135.5634" barWidth="38.565803571428575"></path><path id="SvgjsPath1649" d="M 506.6144196428572 128.238L 506.6144196428572 68.09519999999999Q 506.6144196428572 56.09519999999999 518.6144196428572 56.09519999999999L 527.1802232142858 56.09519999999999Q 539.1802232142858 56.09519999999999 539.1802232142858 68.09519999999999L 539.1802232142858 68.09519999999999L 539.1802232142858 128.238Q 539.1802232142858 140.238 527.1802232142858 140.238L 518.6144196428572 140.238Q 506.6144196428572 140.238 506.6144196428572 128.238z" fill="rgba(105,108,255,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 506.6144196428572 128.238L 506.6144196428572 68.09519999999999Q 506.6144196428572 56.09519999999999 518.6144196428572 56.09519999999999L 527.1802232142858 56.09519999999999Q 539.1802232142858 56.09519999999999 539.1802232142858 68.09519999999999L 539.1802232142858 68.09519999999999L 539.1802232142858 128.238Q 539.1802232142858 140.238 527.1802232142858 140.238L 518.6144196428572 140.238Q 506.6144196428572 140.238 506.6144196428572 128.238z" pathFrom="M 506.6144196428572 128.238L 506.6144196428572 128.238L 539.1802232142858 128.238L 539.1802232142858 128.238L 539.1802232142858 128.238L 539.1802232142858 128.238L 539.1802232142858 128.238L 506.6144196428572 128.238" cy="56.09519999999999" cx="620.4804910714286" j="4" val="18" barHeight="84.14280000000001" barWidth="38.565803571428575"></path><path id="SvgjsPath1650" d="M 623.4804910714286 128.238L 623.4804910714286 96.1428Q 623.4804910714286 84.1428 635.4804910714286 84.1428L 644.0462946428571 84.1428Q 656.0462946428571 84.1428 656.0462946428571 96.1428L 656.0462946428571 96.1428L 656.0462946428571 128.238Q 656.0462946428571 140.238 644.0462946428571 140.238L 635.4804910714286 140.238Q 623.4804910714286 140.238 623.4804910714286 128.238z" fill="rgba(105,108,255,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 623.4804910714286 128.238L 623.4804910714286 96.1428Q 623.4804910714286 84.1428 635.4804910714286 84.1428L 644.0462946428571 84.1428Q 656.0462946428571 84.1428 656.0462946428571 96.1428L 656.0462946428571 96.1428L 656.0462946428571 128.238Q 656.0462946428571 140.238 644.0462946428571 140.238L 635.4804910714286 140.238Q 623.4804910714286 140.238 623.4804910714286 128.238z" pathFrom="M 623.4804910714286 128.238L 623.4804910714286 128.238L 656.0462946428571 128.238L 656.0462946428571 128.238L 656.0462946428571 128.238L 656.0462946428571 128.238L 656.0462946428571 128.238L 623.4804910714286 128.238" cy="84.1428" cx="737.3465625" j="5" val="12" barHeight="56.095200000000006" barWidth="38.565803571428575"></path><path id="SvgjsPath1651" d="M 740.3465625 128.238L 740.3465625 110.16659999999999Q 740.3465625 98.16659999999999 752.3465625 98.16659999999999L 760.9123660714286 98.16659999999999Q 772.9123660714286 98.16659999999999 772.9123660714286 110.16659999999999L 772.9123660714286 110.16659999999999L 772.9123660714286 128.238Q 772.9123660714286 140.238 760.9123660714286 140.238L 752.3465625 140.238Q 740.3465625 140.238 740.3465625 128.238z" fill="rgba(105,108,255,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="0" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 740.3465625 128.238L 740.3465625 110.16659999999999Q 740.3465625 98.16659999999999 752.3465625 98.16659999999999L 760.9123660714286 98.16659999999999Q 772.9123660714286 98.16659999999999 772.9123660714286 110.16659999999999L 772.9123660714286 110.16659999999999L 772.9123660714286 128.238Q 772.9123660714286 140.238 760.9123660714286 140.238L 752.3465625 140.238Q 740.3465625 140.238 740.3465625 128.238z" pathFrom="M 740.3465625 128.238L 740.3465625 128.238L 772.9123660714286 128.238L 772.9123660714286 128.238L 772.9123660714286 128.238L 772.9123660714286 128.238L 772.9123660714286 128.238L 740.3465625 128.238" cy="98.16659999999999" cx="854.2126339285714" j="6" val="9" barHeight="42.071400000000004" barWidth="38.565803571428575"></path></g><g id="SvgjsG1652" class="apexcharts-series" seriesName="2023" rel="2" data:realIndex="1"><path id="SvgjsPath1654" d="M 39.15013392857143 164.238L 39.15013392857143 201.0078Q 39.15013392857143 213.0078 51.15013392857143 213.0078L 59.715937499999995 213.0078Q 71.7159375 213.0078 71.7159375 201.0078L 71.7159375 201.0078L 71.7159375 164.238Q 71.7159375 152.238 59.715937499999995 152.238L 51.15013392857143 152.238Q 39.15013392857143 152.238 39.15013392857143 164.238z" fill="rgba(3,195,236,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 39.15013392857143 164.238L 39.15013392857143 201.0078Q 39.15013392857143 213.0078 51.15013392857143 213.0078L 59.715937499999995 213.0078Q 71.7159375 213.0078 71.7159375 201.0078L 71.7159375 201.0078L 71.7159375 164.238Q 71.7159375 152.238 59.715937499999995 152.238L 51.15013392857143 152.238Q 39.15013392857143 152.238 39.15013392857143 164.238z" pathFrom="M 39.15013392857143 164.238L 39.15013392857143 164.238L 71.7159375 164.238L 71.7159375 164.238L 71.7159375 164.238L 71.7159375 164.238L 71.7159375 164.238L 39.15013392857143 164.238" cy="189.0078" cx="153.01620535714287" j="0" val="-13" barHeight="-60.769800000000004" barWidth="38.565803571428575"></path><path id="SvgjsPath1655" d="M 156.01620535714287 164.238L 156.01620535714287 224.38080000000002Q 156.01620535714287 236.38080000000002 168.01620535714287 236.38080000000002L 176.58200892857144 236.38080000000002Q 188.58200892857144 236.38080000000002 188.58200892857144 224.38080000000002L 188.58200892857144 224.38080000000002L 188.58200892857144 164.238Q 188.58200892857144 152.238 176.58200892857144 152.238L 168.01620535714287 152.238Q 156.01620535714287 152.238 156.01620535714287 164.238z" fill="rgba(3,195,236,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 156.01620535714287 164.238L 156.01620535714287 224.38080000000002Q 156.01620535714287 236.38080000000002 168.01620535714287 236.38080000000002L 176.58200892857144 236.38080000000002Q 188.58200892857144 236.38080000000002 188.58200892857144 224.38080000000002L 188.58200892857144 224.38080000000002L 188.58200892857144 164.238Q 188.58200892857144 152.238 176.58200892857144 152.238L 168.01620535714287 152.238Q 156.01620535714287 152.238 156.01620535714287 164.238z" pathFrom="M 156.01620535714287 164.238L 156.01620535714287 164.238L 188.58200892857144 164.238L 188.58200892857144 164.238L 188.58200892857144 164.238L 188.58200892857144 164.238L 188.58200892857144 164.238L 156.01620535714287 164.238" cy="212.38080000000002" cx="269.8822767857143" j="1" val="-18" barHeight="-84.14280000000001" barWidth="38.565803571428575"></path><path id="SvgjsPath1656" d="M 272.8822767857143 164.238L 272.8822767857143 182.3094Q 272.8822767857143 194.3094 284.8822767857143 194.3094L 293.4480803571429 194.3094Q 305.4480803571429 194.3094 305.4480803571429 182.3094L 305.4480803571429 182.3094L 305.4480803571429 164.238Q 305.4480803571429 152.238 293.4480803571429 152.238L 284.8822767857143 152.238Q 272.8822767857143 152.238 272.8822767857143 164.238z" fill="rgba(3,195,236,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 272.8822767857143 164.238L 272.8822767857143 182.3094Q 272.8822767857143 194.3094 284.8822767857143 194.3094L 293.4480803571429 194.3094Q 305.4480803571429 194.3094 305.4480803571429 182.3094L 305.4480803571429 182.3094L 305.4480803571429 164.238Q 305.4480803571429 152.238 293.4480803571429 152.238L 284.8822767857143 152.238Q 272.8822767857143 152.238 272.8822767857143 164.238z" pathFrom="M 272.8822767857143 164.238L 272.8822767857143 164.238L 305.4480803571429 164.238L 305.4480803571429 164.238L 305.4480803571429 164.238L 305.4480803571429 164.238L 305.4480803571429 164.238L 272.8822767857143 164.238" cy="170.3094" cx="386.7483482142857" j="2" val="-9" barHeight="-42.071400000000004" barWidth="38.565803571428575"></path><path id="SvgjsPath1657" d="M 389.7483482142857 164.238L 389.7483482142857 205.6824Q 389.7483482142857 217.6824 401.7483482142857 217.6824L 410.31415178571433 217.6824Q 422.31415178571433 217.6824 422.31415178571433 205.6824L 422.31415178571433 205.6824L 422.31415178571433 164.238Q 422.31415178571433 152.238 410.31415178571433 152.238L 401.7483482142857 152.238Q 389.7483482142857 152.238 389.7483482142857 164.238z" fill="rgba(3,195,236,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 389.7483482142857 164.238L 389.7483482142857 205.6824Q 389.7483482142857 217.6824 401.7483482142857 217.6824L 410.31415178571433 217.6824Q 422.31415178571433 217.6824 422.31415178571433 205.6824L 422.31415178571433 205.6824L 422.31415178571433 164.238Q 422.31415178571433 152.238 410.31415178571433 152.238L 401.7483482142857 152.238Q 389.7483482142857 152.238 389.7483482142857 164.238z" pathFrom="M 389.7483482142857 164.238L 389.7483482142857 164.238L 422.31415178571433 164.238L 422.31415178571433 164.238L 422.31415178571433 164.238L 422.31415178571433 164.238L 422.31415178571433 164.238L 389.7483482142857 164.238" cy="193.6824" cx="503.6144196428572" j="3" val="-14" barHeight="-65.4444" barWidth="38.565803571428575"></path><path id="SvgjsPath1658" d="M 506.6144196428572 164.238L 506.6144196428572 163.611Q 506.6144196428572 175.611 518.6144196428572 175.611L 527.1802232142858 175.611Q 539.1802232142858 175.611 539.1802232142858 163.611L 539.1802232142858 163.611L 539.1802232142858 164.238Q 539.1802232142858 152.238 527.1802232142858 152.238L 518.6144196428572 152.238Q 506.6144196428572 152.238 506.6144196428572 164.238z" fill="rgba(3,195,236,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 506.6144196428572 164.238L 506.6144196428572 163.611Q 506.6144196428572 175.611 518.6144196428572 175.611L 527.1802232142858 175.611Q 539.1802232142858 175.611 539.1802232142858 163.611L 539.1802232142858 163.611L 539.1802232142858 164.238Q 539.1802232142858 152.238 527.1802232142858 152.238L 518.6144196428572 152.238Q 506.6144196428572 152.238 506.6144196428572 164.238z" pathFrom="M 506.6144196428572 164.238L 506.6144196428572 164.238L 539.1802232142858 164.238L 539.1802232142858 164.238L 539.1802232142858 164.238L 539.1802232142858 164.238L 539.1802232142858 164.238L 506.6144196428572 164.238" cy="151.611" cx="620.4804910714286" j="4" val="-5" barHeight="-23.373" barWidth="38.565803571428575"></path><path id="SvgjsPath1659" d="M 623.4804910714286 164.238L 623.4804910714286 219.70620000000002Q 623.4804910714286 231.70620000000002 635.4804910714286 231.70620000000002L 644.0462946428571 231.70620000000002Q 656.0462946428571 231.70620000000002 656.0462946428571 219.70620000000002L 656.0462946428571 219.70620000000002L 656.0462946428571 164.238Q 656.0462946428571 152.238 644.0462946428571 152.238L 635.4804910714286 152.238Q 623.4804910714286 152.238 623.4804910714286 164.238z" fill="rgba(3,195,236,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 623.4804910714286 164.238L 623.4804910714286 219.70620000000002Q 623.4804910714286 231.70620000000002 635.4804910714286 231.70620000000002L 644.0462946428571 231.70620000000002Q 656.0462946428571 231.70620000000002 656.0462946428571 219.70620000000002L 656.0462946428571 219.70620000000002L 656.0462946428571 164.238Q 656.0462946428571 152.238 644.0462946428571 152.238L 635.4804910714286 152.238Q 623.4804910714286 152.238 623.4804910714286 164.238z" pathFrom="M 623.4804910714286 164.238L 623.4804910714286 164.238L 656.0462946428571 164.238L 656.0462946428571 164.238L 656.0462946428571 164.238L 656.0462946428571 164.238L 656.0462946428571 164.238L 623.4804910714286 164.238" cy="207.70620000000002" cx="737.3465625" j="5" val="-17" barHeight="-79.46820000000001" barWidth="38.565803571428575"></path><path id="SvgjsPath1660" d="M 740.3465625 164.238L 740.3465625 210.357Q 740.3465625 222.357 752.3465625 222.357L 760.9123660714286 222.357Q 772.9123660714286 222.357 772.9123660714286 210.357L 772.9123660714286 210.357L 772.9123660714286 164.238Q 772.9123660714286 152.238 760.9123660714286 152.238L 752.3465625 152.238Q 740.3465625 152.238 740.3465625 164.238z" fill="rgba(3,195,236,1)" fill-opacity="1" stroke="#ffffff" stroke-opacity="1" stroke-linecap="round" stroke-width="6" stroke-dasharray="0" class="apexcharts-bar-area" index="1" clip-path="url(#gridRectMaskw9gc832wj)" pathTo="M 740.3465625 164.238L 740.3465625 210.357Q 740.3465625 222.357 752.3465625 222.357L 760.9123660714286 222.357Q 772.9123660714286 222.357 772.9123660714286 210.357L 772.9123660714286 210.357L 772.9123660714286 164.238Q 772.9123660714286 152.238 760.9123660714286 152.238L 752.3465625 152.238Q 740.3465625 152.238 740.3465625 164.238z" pathFrom="M 740.3465625 164.238L 740.3465625 164.238L 772.9123660714286 164.238L 772.9123660714286 164.238L 772.9123660714286 164.238L 772.9123660714286 164.238L 772.9123660714286 164.238L 740.3465625 164.238" cy="198.357" cx="854.2126339285714" j="6" val="-15" barHeight="-70.119" barWidth="38.565803571428575"></path></g><g id="SvgjsG1644" class="apexcharts-datalabels" data:realIndex="0"></g><g id="SvgjsG1653" class="apexcharts-datalabels" data:realIndex="1"></g></g><line id="SvgjsLine1709" x1="0" y1="0" x2="818.0625" y2="0" stroke="#b6b6b6" stroke-dasharray="0" stroke-width="1" stroke-linecap="butt" class="apexcharts-ycrosshairs"></line><line id="SvgjsLine1710" x1="0" y1="0" x2="818.0625" y2="0" stroke-dasharray="0" stroke-width="0" stroke-linecap="butt" class="apexcharts-ycrosshairs-hidden"></line><g id="SvgjsG1711" class="apexcharts-yaxis-annotations"></g><g id="SvgjsG1712" class="apexcharts-xaxis-annotations"></g><g id="SvgjsG1713" class="apexcharts-point-annotations"></g></g><g id="SvgjsG1684" class="apexcharts-yaxis" rel="0" transform="translate(18.9375, 0)"><g id="SvgjsG1685" class="apexcharts-yaxis-texts-g"><text id="SvgjsText1686" font-family="Public Sans" x="20" y="53.5" text-anchor="end" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-yaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1687">30</tspan><title>30</title></text><text id="SvgjsText1688" font-family="Public Sans" x="20" y="100.24600000000001" text-anchor="end" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-yaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1689">20</tspan><title>20</title></text><text id="SvgjsText1690" font-family="Public Sans" x="20" y="146.99200000000002" text-anchor="end" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-yaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1691">10</tspan><title>10</title></text><text id="SvgjsText1692" font-family="Public Sans" x="20" y="193.73800000000003" text-anchor="end" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-yaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1693">0</tspan><title>0</title></text><text id="SvgjsText1694" font-family="Public Sans" x="20" y="240.48400000000004" text-anchor="end" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-yaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1695">-10</tspan><title>-10</title></text><text id="SvgjsText1696" font-family="Public Sans" x="20" y="287.23" text-anchor="end" dominant-baseline="auto" font-size="13px" font-weight="400" fill="#a7acb2" class="apexcharts-text apexcharts-yaxis-label " style="font-family: &quot;Public Sans&quot;;"><tspan id="SvgjsTspan1697">-20</tspan><title>-20</title></text></g></g><g id="SvgjsG1632" class="apexcharts-annotations"></g></svg><div class="apexcharts-tooltip apexcharts-theme-light"><div class="apexcharts-tooltip-title" style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;"></div><div class="apexcharts-tooltip-series-group" style="order: 1;"><span class="apexcharts-tooltip-marker" style="background-color: rgb(105, 108, 255);"></span><div class="apexcharts-tooltip-text" style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;"><div class="apexcharts-tooltip-y-group"><span class="apexcharts-tooltip-text-y-label"></span><span class="apexcharts-tooltip-text-y-value"></span></div><div class="apexcharts-tooltip-goals-group"><span class="apexcharts-tooltip-text-goals-label"></span><span class="apexcharts-tooltip-text-goals-value"></span></div><div class="apexcharts-tooltip-z-group"><span class="apexcharts-tooltip-text-z-label"></span><span class="apexcharts-tooltip-text-z-value"></span></div></div></div><div class="apexcharts-tooltip-series-group" style="order: 2;"><span class="apexcharts-tooltip-marker" style="background-color: rgb(3, 195, 236);"></span><div class="apexcharts-tooltip-text" style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;"><div class="apexcharts-tooltip-y-group"><span class="apexcharts-tooltip-text-y-label"></span><span class="apexcharts-tooltip-text-y-value"></span></div><div class="apexcharts-tooltip-goals-group"><span class="apexcharts-tooltip-text-goals-label"></span><span class="apexcharts-tooltip-text-goals-value"></span></div><div class="apexcharts-tooltip-z-group"><span class="apexcharts-tooltip-text-z-label"></span><span class="apexcharts-tooltip-text-z-value"></span></div></div></div></div><div class="apexcharts-yaxistooltip apexcharts-yaxistooltip-0 apexcharts-yaxistooltip-left apexcharts-theme-light"><div class="apexcharts-yaxistooltip-text"></div></div></div></div>
                <div class="resize-triggers"><div class="expand-trigger"><div style="width: 920px; height: 410px;"></div></div><div class="contract-trigger"></div></div></div>
                <div class="col-lg-4 d-flex align-items-center">
                    <div class="card-body px-xl-9" style="position: relative;">
                        <div class="text-center mb-6">
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-primary">
                                    <script>
                                        document.write(new Date().getFullYear() - 1)
                                    </script>2024
                                </button>
                                <button type="button" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                    <span class="visually-hidden">Toggle Dropdown</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="javascript:void(0);">2021</a></li>
                                    <li><a class="dropdown-item" href="javascript:void(0);">2020</a></li>
                                    <li><a class="dropdown-item" href="javascript:void(0);">2019</a></li>
                                </ul>
                            </div>
                        </div>

                        <div id="growthChart" style="min-height: 154.875px;"><div id="apexchartshfvbaxu6" class="apexcharts-canvas apexchartshfvbaxu6 apexcharts-theme-light" style="width: 871px; height: 154.875px;"><svg id="SvgjsSvg1714" width="871" height="154.875" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev" class="apexcharts-svg" xmlns:data="ApexChartsNS" transform="translate(0, 0)" style="background: transparent;"><g id="SvgjsG1716" class="apexcharts-inner apexcharts-graphical" transform="translate(328.5, -25)"><defs id="SvgjsDefs1715"><clipPath id="gridRectMaskhfvbaxu6"><rect id="SvgjsRect1718" width="222" height="285" x="-3" y="-1" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath><clipPath id="forecastMaskhfvbaxu6"></clipPath><clipPath id="nonForecastMaskhfvbaxu6"></clipPath><clipPath id="gridRectMarkerMaskhfvbaxu6"><rect id="SvgjsRect1719" width="220" height="287" x="-2" y="-2" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath><linearGradient id="SvgjsLinearGradient1724" x1="1" y1="0" x2="0" y2="1"><stop id="SvgjsStop1725" stop-opacity="1" stop-color="rgba(105,108,255,1)" offset="0.3"></stop><stop id="SvgjsStop1726" stop-opacity="0.6" stop-color="rgba(255,255,255,0.6)" offset="0.7"></stop><stop id="SvgjsStop1727" stop-opacity="0.6" stop-color="rgba(255,255,255,0.6)" offset="1"></stop></linearGradient><linearGradient id="SvgjsLinearGradient1735" x1="1" y1="0" x2="0" y2="1"><stop id="SvgjsStop1736" stop-opacity="1" stop-color="rgba(105,108,255,1)" offset="0.3"></stop><stop id="SvgjsStop1737" stop-opacity="0.6" stop-color="rgba(105,108,255,0.6)" offset="0.7"></stop><stop id="SvgjsStop1738" stop-opacity="0.6" stop-color="rgba(105,108,255,0.6)" offset="1"></stop></linearGradient></defs><g id="SvgjsG1720" class="apexcharts-radialbar"><g id="SvgjsG1721"><g id="SvgjsG1722" class="apexcharts-tracks"><g id="SvgjsG1723" class="apexcharts-radialbar-track apexcharts-track" rel="1"><path id="apexcharts-radialbarTrack-0" d="M 73.83506097560974 167.17541022773656 A 68.32987804878049 68.32987804878049 0 1 1 142.16493902439026 167.17541022773656" fill="none" fill-opacity="1" stroke="rgba(255,255,255,0.85)" stroke-opacity="1" stroke-linecap="butt" stroke-width="17.357317073170734" stroke-dasharray="0" class="apexcharts-radialbar-area" data:pathOrig="M 73.83506097560974 167.17541022773656 A 68.32987804878049 68.32987804878049 0 1 1 142.16493902439026 167.17541022773656"></path></g></g><g id="SvgjsG1729"><g id="SvgjsG1734" class="apexcharts-series apexcharts-radial-series" seriesName="Growth" rel="1" data:realIndex="0"><path id="SvgjsPath1739" d="M 73.83506097560974 167.17541022773656 A 68.32987804878049 68.32987804878049 0 1 1 175.95555982735613 100.85758285229481" fill="none" fill-opacity="0.85" stroke="url(#SvgjsLinearGradient1735)" stroke-opacity="1" stroke-linecap="butt" stroke-width="17.357317073170734" stroke-dasharray="5" class="apexcharts-radialbar-area apexcharts-radialbar-slice-0" data:angle="234" data:value="78" index="0" j="0" data:pathOrig="M 73.83506097560974 167.17541022773656 A 68.32987804878049 68.32987804878049 0 1 1 175.95555982735613 100.85758285229481"></path></g><circle id="SvgjsCircle1730" r="54.65121951219512" cx="108" cy="108" class="apexcharts-radialbar-hollow" fill="transparent"></circle><g id="SvgjsG1731" class="apexcharts-datalabels-group" transform="translate(0, 0) scale(1)" style="opacity: 1;"><text id="SvgjsText1732" font-family="Public Sans" x="108" y="123" text-anchor="middle" dominant-baseline="auto" font-size="15px" font-weight="500" fill="#646e78" class="apexcharts-text apexcharts-datalabel-label" style="font-family: &quot;Public Sans&quot;;">Growth</text><text id="SvgjsText1733" font-family="Public Sans" x="108" y="99" text-anchor="middle" dominant-baseline="auto" font-size="22px" font-weight="500" fill="#384551" class="apexcharts-text apexcharts-datalabel-value" style="font-family: &quot;Public Sans&quot;;">78%</text></g></g></g></g><line id="SvgjsLine1740" x1="0" y1="0" x2="216" y2="0" stroke="#b6b6b6" stroke-dasharray="0" stroke-width="1" stroke-linecap="butt" class="apexcharts-ycrosshairs"></line><line id="SvgjsLine1741" x1="0" y1="0" x2="216" y2="0" stroke-dasharray="0" stroke-width="0" stroke-linecap="butt" class="apexcharts-ycrosshairs-hidden"></line></g><g id="SvgjsG1717" class="apexcharts-annotations"></g></svg><div class="apexcharts-legend"></div></div></div>
                        <div class="text-center fw-medium my-6">62% Company Growth</div>

                        <div class="d-flex gap-3 justify-content-between">
                            <div class="d-flex">
                                <div class="avatar me-2">
                                    <span class="avatar-initial rounded-2 bg-label-primary"><i class="bx bx-dollar bx-lg text-primary"></i></span>
                                </div>
                                <div class="d-flex flex-column">
                                    <small>
                                        <script>
                                            document.write(new Date().getFullYear() - 1)
                                        </script>2024
                                    </small>
                                    <h6 class="mb-0">$32.5k</h6>
                                </div>
                            </div>
                            <div class="d-flex">
                                <div class="avatar me-2">
                                    <span class="avatar-initial rounded-2 bg-label-info"><i class="bx bx-wallet bx-lg text-info"></i></span>
                                </div>
                                <div class="d-flex flex-column">
                                    <small>
                                        <script>
                                            document.write(new Date().getFullYear() - 2)
                                        </script>2023
                                    </small>
                                    <h6 class="mb-0">$41.2k</h6>
                                </div>
                            </div>
                        </div>
                    <div class="resize-triggers"><div class="expand-trigger"><div style="width: 920px; height: 374px;"></div></div><div class="contract-trigger"></div></div></div>
                </div>
            </div>
        </div>
    </div>
    <!--/ Total Revenue -->
    <div class="col-12 col-md-8 col-lg-12 col-xxl-4 order-3 order-md-2">
        <div class="row">
            <div class="col-6 mb-6">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="card-title d-flex align-items-start justify-content-between mb-4">
                            <div class="avatar flex-shrink-0">
                                <img src="http://127.0.0.1:8000/assets/img/icons/unicons/paypal.png" alt="paypal" class="rounded">
                            </div>
                            <div class="dropdown">
                                <button class="btn p-0" type="button" id="cardOpt4" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="bx bx-dots-vertical-rounded text-muted"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="cardOpt4">
                                    <a class="dropdown-item" href="javascript:void(0);">View More</a>
                                    <a class="dropdown-item" href="javascript:void(0);">Delete</a>
                                </div>
                            </div>
                        </div>
                        <p class="mb-1">Payments</p>
                        <h4 class="card-title mb-3">$2,456</h4>
                        <small class="text-danger fw-medium"><i class="bx bx-down-arrow-alt"></i> -14.82%</small>
                    </div>
                </div>
            </div>
            <div class="col-6 mb-6">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="card-title d-flex align-items-start justify-content-between mb-4">
                            <div class="avatar flex-shrink-0">
                                <img src="http://127.0.0.1:8000/assets/img/icons/unicons/cc-primary.png" alt="Credit Card" class="rounded">
                            </div>
                            <div class="dropdown">
                                <button class="btn p-0" type="button" id="cardOpt1" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="bx bx-dots-vertical-rounded text-muted"></i>
                                </button>
                                <div class="dropdown-menu" aria-labelledby="cardOpt1">
                                    <a class="dropdown-item" href="javascript:void(0);">View More</a>
                                    <a class="dropdown-item" href="javascript:void(0);">Delete</a>
                                </div>
                            </div>
                        </div>
                        <p class="mb-1">Transactions</p>
                        <h4 class="card-title mb-3">$14,857</h4>
                        <small class="text-success fw-medium"><i class="bx bx-up-arrow-alt"></i> +28.14%</small>
                    </div>
                </div>
            </div>
            <div class="col-12 mb-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center flex-sm-row flex-column gap-10" style="position: relative;">
                            <div class="d-flex flex-sm-column flex-row align-items-start justify-content-between">
                                <div class="card-title mb-6">
                                    <h5 class="text-nowrap mb-1">Profile Report</h5>
                                    <span class="badge bg-label-warning">YEAR 2022</span>
                                </div>
                                <div class="mt-sm-auto">
                                    <span class="text-success text-nowrap fw-medium"><i class="bx bx-up-arrow-alt"></i> 68.2%</span>
                                    <h4 class="mb-0">$84,686k</h4>
                                </div>
                            </div>
                            <div id="profileReportChart" style="min-height: 75px;"><div id="apexchartsvbu8cgpq" class="apexcharts-canvas apexchartsvbu8cgpq apexcharts-theme-light" style="width: 300px; height: 75px;"><svg id="SvgjsSvg1581" width="300" height="75" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev" class="apexcharts-svg" xmlns:data="ApexChartsNS" transform="translate(0, 0)" style="background: transparent;"><g id="SvgjsG1583" class="apexcharts-inner apexcharts-graphical" transform="translate(0, 0)"><defs id="SvgjsDefs1582"><clipPath id="gridRectMaskvbu8cgpq"><rect id="SvgjsRect1588" width="301" height="80" x="-4.5" y="-2.5" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath><clipPath id="forecastMaskvbu8cgpq"></clipPath><clipPath id="nonForecastMaskvbu8cgpq"></clipPath><clipPath id="gridRectMarkerMaskvbu8cgpq"><rect id="SvgjsRect1589" width="296" height="79" x="-2" y="-2" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fff"></rect></clipPath><filter id="SvgjsFilter1595" filterUnits="userSpaceOnUse" width="200%" height="200%" x="-50%" y="-50%"><feFlood id="SvgjsFeFlood1596" flood-color="#ffab00" flood-opacity="0.15" result="SvgjsFeFlood1596Out" in="SourceGraphic"></feFlood><feComposite id="SvgjsFeComposite1597" in="SvgjsFeFlood1596Out" in2="SourceAlpha" operator="in" result="SvgjsFeComposite1597Out"></feComposite><feOffset id="SvgjsFeOffset1598" dx="5" dy="10" result="SvgjsFeOffset1598Out" in="SvgjsFeComposite1597Out"></feOffset><feGaussianBlur id="SvgjsFeGaussianBlur1599" stdDeviation="3 " result="SvgjsFeGaussianBlur1599Out" in="SvgjsFeOffset1598Out"></feGaussianBlur><feMerge id="SvgjsFeMerge1600" result="SvgjsFeMerge1600Out" in="SourceGraphic"><feMergeNode id="SvgjsFeMergeNode1601" in="SvgjsFeGaussianBlur1599Out"></feMergeNode><feMergeNode id="SvgjsFeMergeNode1602" in="[object Arguments]"></feMergeNode></feMerge><feBlend id="SvgjsFeBlend1603" in="SourceGraphic" in2="SvgjsFeMerge1600Out" mode="normal" result="SvgjsFeBlend1603Out"></feBlend></filter></defs><line id="SvgjsLine1587" x1="0" y1="0" x2="0" y2="75" stroke="#b6b6b6" stroke-dasharray="3" stroke-linecap="butt" class="apexcharts-xcrosshairs" x="0" y="0" width="1" height="75" fill="#b1b9c4" filter="none" fill-opacity="0.9" stroke-width="1"></line><g id="SvgjsG1604" class="apexcharts-xaxis" transform="translate(0, 0)"><g id="SvgjsG1605" class="apexcharts-xaxis-texts-g" transform="translate(0, -4)"></g></g><g id="SvgjsG1613" class="apexcharts-grid"><g id="SvgjsG1614" class="apexcharts-gridlines-horizontal" style="display: none;"><line id="SvgjsLine1616" x1="0" y1="0" x2="292" y2="0" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1617" x1="0" y1="18.75" x2="292" y2="18.75" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1618" x1="0" y1="37.5" x2="292" y2="37.5" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1619" x1="0" y1="56.25" x2="292" y2="56.25" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line><line id="SvgjsLine1620" x1="0" y1="75" x2="292" y2="75" stroke="#e0e0e0" stroke-dasharray="0" stroke-linecap="butt" class="apexcharts-gridline"></line></g><g id="SvgjsG1615" class="apexcharts-gridlines-vertical" style="display: none;"></g><line id="SvgjsLine1622" x1="0" y1="75" x2="292" y2="75" stroke="transparent" stroke-dasharray="0" stroke-linecap="butt"></line><line id="SvgjsLine1621" x1="0" y1="1" x2="0" y2="75" stroke="transparent" stroke-dasharray="0" stroke-linecap="butt"></line></g><g id="SvgjsG1590" class="apexcharts-line-series apexcharts-plot-series"><g id="SvgjsG1591" class="apexcharts-series" seriesName="seriesx1" data:longestSeries="true" rel="1" data:realIndex="0"><path id="SvgjsPath1594" d="M 0 71.25C 20.44 71.25 37.96000000000001 11.25 58.400000000000006 11.25C 78.84 11.25 96.36000000000001 58.125 116.80000000000001 58.125C 137.24 58.125 154.76000000000002 20.625 175.20000000000002 20.625C 195.64000000000001 20.625 213.16000000000003 35.625 233.60000000000002 35.625C 254.04000000000002 35.625 271.56 5.625 292 5.625" fill="none" fill-opacity="1" stroke="rgba(255,171,0,0.85)" stroke-opacity="1" stroke-linecap="butt" stroke-width="5" stroke-dasharray="0" class="apexcharts-line" index="0" clip-path="url(#gridRectMaskvbu8cgpq)" filter="url(#SvgjsFilter1595)" pathTo="M 0 71.25C 20.44 71.25 37.96000000000001 11.25 58.400000000000006 11.25C 78.84 11.25 96.36000000000001 58.125 116.80000000000001 58.125C 137.24 58.125 154.76000000000002 20.625 175.20000000000002 20.625C 195.64000000000001 20.625 213.16000000000003 35.625 233.60000000000002 35.625C 254.04000000000002 35.625 271.56 5.625 292 5.625" pathFrom="M -1 112.5L -1 112.5L 58.400000000000006 112.5L 116.80000000000001 112.5L 175.20000000000002 112.5L 233.60000000000002 112.5L 292 112.5"></path><g id="SvgjsG1592" class="apexcharts-series-markers-wrap" data:realIndex="0"><g class="apexcharts-series-markers"><circle id="SvgjsCircle1628" r="0" cx="0" cy="0" class="apexcharts-marker wljjbq635 no-pointer-events" stroke="#ffffff" fill="#ffab00" fill-opacity="1" stroke-width="2" stroke-opacity="0.9" default-marker-size="0"></circle></g></g></g><g id="SvgjsG1593" class="apexcharts-datalabels" data:realIndex="0"></g></g><line id="SvgjsLine1623" x1="0" y1="0" x2="292" y2="0" stroke="#b6b6b6" stroke-dasharray="0" stroke-width="1" stroke-linecap="butt" class="apexcharts-ycrosshairs"></line><line id="SvgjsLine1624" x1="0" y1="0" x2="292" y2="0" stroke-dasharray="0" stroke-width="0" stroke-linecap="butt" class="apexcharts-ycrosshairs-hidden"></line><g id="SvgjsG1625" class="apexcharts-yaxis-annotations"></g><g id="SvgjsG1626" class="apexcharts-xaxis-annotations"></g><g id="SvgjsG1627" class="apexcharts-point-annotations"></g></g><rect id="SvgjsRect1586" width="0" height="0" x="0" y="0" rx="0" ry="0" opacity="1" stroke-width="0" stroke="none" stroke-dasharray="0" fill="#fefefe"></rect><g id="SvgjsG1612" class="apexcharts-yaxis" rel="0" transform="translate(-18, 0)"></g><g id="SvgjsG1584" class="apexcharts-annotations"></g></svg><div class="apexcharts-legend" style="max-height: 37.5px;"></div><div class="apexcharts-tooltip apexcharts-theme-light"><div class="apexcharts-tooltip-title" style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;"></div><div class="apexcharts-tooltip-series-group" style="order: 1;"><span class="apexcharts-tooltip-marker" style="background-color: rgb(255, 171, 0);"></span><div class="apexcharts-tooltip-text" style="font-family: Helvetica, Arial, sans-serif; font-size: 12px;"><div class="apexcharts-tooltip-y-group"><span class="apexcharts-tooltip-text-y-label"></span><span class="apexcharts-tooltip-text-y-value"></span></div><div class="apexcharts-tooltip-goals-group"><span class="apexcharts-tooltip-text-goals-label"></span><span class="apexcharts-tooltip-text-goals-value"></span></div><div class="apexcharts-tooltip-z-group"><span class="apexcharts-tooltip-text-z-label"></span><span class="apexcharts-tooltip-text-z-value"></span></div></div></div></div><div class="apexcharts-yaxistooltip apexcharts-yaxistooltip-0 apexcharts-yaxistooltip-left apexcharts-theme-light"><div class="apexcharts-yaxistooltip-text"></div></div></div></div>
                        <div class="resize-triggers"><div class="expand-trigger"><div style="width: 557px; height: 141px;"></div></div><div class="contract-trigger"></div></div></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection