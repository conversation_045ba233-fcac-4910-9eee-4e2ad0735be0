@php
use Illuminate\Support\Facades\Auth;
@endphp

<style>
    #layout-menu {
        height: 100dvh; /* ✅ gunakan dynamic viewport */
    }
    .menu-inner {
        max-height: calc(100dvh - 70px); /* contoh: sisakan untuk logo */
    }
    
    .menu-inner {
        padding-bottom: 3rem;
        box-sizing: border-box;
    }
</style>

<aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
    
    <!-- ! Hide app brand if navbar-full -->
    <div class="app-brand demo">
        <a href="{{ url('/') }}" class="app-brand-link">
            <span class="app-brand-logo demo">
                <img src="{{ asset('assets/nagih.svg') }}" alt="Logo" height="150" width="100%">
            </span>
        </a>
        
        <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto d-block d-xl-none">
            <i class="bx bx-chevron-left bx-sm d-flex align-items-center justify-content-center"></i>
        </a>
    </div>
    
    <div class="menu-inner-shadow"></div>
    
    <ul class="menu-inner py-1">
        @foreach ($menuData[0]->menu as $menu)
        @if (isset($menu->roles) && in_array(Auth::user()->roles->name, $menu->roles))
        {{-- adding active and open class if child is active --}}
        
        {{-- menu headers --}}
        @if (isset($menu->menuHeader))
        <li class="menu-header small text-uppercase">
            <span class="menu-header-text">{{ __($menu->menuHeader) }}</span>
        </li>
        @else
        {{-- active menu method --}}
        @php
        $activeClass = null;
        $currentRouteName = Route::currentRouteName();
        
        if ($currentRouteName === $menu->slug) {
            $activeClass = 'active';
        } elseif (isset($menu->submenu)) {
            if (gettype($menu->slug) === 'array') {
                foreach ($menu->slug as $slug) {
                    if (
                    str_contains($currentRouteName, $slug) and
                    strpos($currentRouteName, $slug) === 0
                    ) {
                        $activeClass = 'active open';
                    }
                }
            } else {
                if (
                str_contains($currentRouteName, $menu->slug) and
                strpos($currentRouteName, $menu->slug) === 0
                ) {
                    $activeClass = 'active open';
                }
            }
        }
        @endphp
        
        {{-- main menu --}}
        <li class="menu-item {{ $activeClass }}">
            <a href="{{ isset($menu->url) ? url($menu->url) : 'javascript:void(0);' }}"
                class="{{ isset($menu->submenu) ? 'menu-link menu-toggle' : 'menu-link' }}"
                @if (isset($menu->target) and !empty($menu->target)) target="_blank" @endif>
                @isset($menu->icon)
                <i class="{{ $menu->icon }}"></i>
                @endisset
                <div>{{ isset($menu->name) ? __($menu->name) : '' }}</div>
                @isset($menu->badge)
                <div class="badge rounded-pill bg-{{ $menu->badge[0] }} text-uppercase ms-auto">
                    {{ $menu->badge[1] }}</div>
                    @endisset
                </a>
                
                {{-- submenu --}}
                @isset($menu->submenu)
                <ul class="menu-sub">
                    @foreach ($menu->submenu as $submenu)
                    @if (isset($submenu->roles) && in_array(Auth::user()->roles->name, $submenu->roles))
                    <li
                    class="menu-item {{ $submenu->slug === Route::currentRouteName() ? 'active' : '' }}">
                    <a href="{{ isset($submenu->url) ? url($submenu->url) : 'javascript:void(0)' }}"
                        class="menu-link">
                        <div>{{ isset($submenu->name) ? __($submenu->name) : '' }}</div>
                    </a>
                </li>
                @endif
                @endforeach
            </ul>
            @endisset
        </li>
        @endif
        @endif
        @endforeach
    </ul>
    
</aside>
