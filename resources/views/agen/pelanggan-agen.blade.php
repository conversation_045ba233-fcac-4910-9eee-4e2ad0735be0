@extends('layouts.contentNavbarLayout')

@section('title', 'Data Pelanggan Agen')


@section('content')
<div class="row">
    <div class="col-12">
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title fw-bold">Daftar Pelanggan dibawah Agen {{auth()->user()->name}}</h5>
                <small class="card-subtitle">Daftar seluruh pelanggan yang terdaftar dibawah agen {{auth()->user()->name}}</small>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead class="text-center table-dark table-hover">
                            <th>No</th>
                            <th>Nama Pelanggan</th>
                            <th>No. HP</th>
                            <th>Alamat</th>
                            <th>Paket</th>
                            <th>Status</th>
                        </thead>
                        <tbody class="text-center">
                            @php
                                $no = 1;
                            @endphp
                            @forelse ($pelanggan as $item)
                                <td>{{$no++}}</td>
                                <td>{{ $item->nama_customer }}</td>
                                <td>{{$item->no_hp}}</td>
                                <td>{{$item->alamat}}</td>
                                <td>
                                    <span class="badge bg-warning bg-opacity-10 text-warning">
                                        {{$item->paket->nama_paket}}
                                    </span>
                                </td>
                                <td>
                                    @if($item->status_id == 3)
                                    <span class="badge bg-success bg-opacity-10 text-success">
                                        Aktif
                                    </span>
                                    @elseif($item->status_id == 1)
                                    <span class="badge bg-warning bg-opacity-10 text-warning">
                                        Menunggu
                                    </span>
                                    @elseif($item->status_id == 9)
                                    <span class="badge bg-danger bg-opacity-10 text-danger">
                                        Blokir
                                    </span>
                                    @endif
                                </td>
                            @empty
                                
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection