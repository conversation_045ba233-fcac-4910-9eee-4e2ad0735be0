@extends('layouts.contentNavbarLayout')

@section('title', 'Interface Mikrotik')

@section('content')
<div class="container">
    <h5 class="mb-4">Realtime Traffic Monitoring</h5>
    <canvas id="trafficChart" height="120"></canvas>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    const ctx = document.getElementById('trafficChart').getContext('2d');

    let chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Download (Rx)',
                data: [],
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                fill: false
            }, {
                label: 'Upload (Tx)',
                data: [],
                borderColor: 'rgba(255, 99, 132, 1)',
                borderWidth: 2,
                fill: false
            }]
        },
        options: {
            responsive: true,
            animation: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Waktu'
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Ke<PERSON>pa<PERSON> (bps)'
                    }
                }
            }
        }
    });

    function updateChart() {
        fetch(`/noc/interface/{{ $router_id }}/realtime`)
            .then(response => response.json())
            .then(data => {
                let now = new Date().toLocaleTimeString();
                if (chart.data.labels.length > 20) {
                    chart.data.labels.shift();
                    chart.data.datasets[0].data.shift();
                    chart.data.datasets[1].data.shift();
                }
                chart.data.labels.push(now);
                chart.data.datasets[0].data.push(data.rx);
                chart.data.datasets[1].data.push(data.tx);
                chart.update();
            });
    }

    setInterval(updateChart, 3000); // refresh setiap 3 detik
</script>
@endsection
